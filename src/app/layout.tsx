/**
 * Root Layout
 * Main layout for the entire application
 */

import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'AuthenCIO CMS - Content Generation Platform',
  description: 'AI-powered content creation with human-in-the-loop review and workflow automation',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <style>{`
          * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
          }

          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background-color: #f9fafb;
            color: #333;
            line-height: 1.6;
          }

          .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
          }

          .card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            border: 1px solid #e5e7eb;
            transition: all 0.2s ease-in-out;
          }

          .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
          }

          .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-weight: 500;
            font-size: 0.875rem;
            border: none;
            cursor: pointer;
            transition: all 0.15s ease-in-out;
            text-decoration: none;
          }

          .btn-primary {
            background-color: #3b82f6;
            color: white;
          }

          .btn-primary:hover {
            background-color: #2563eb;
          }

          .grid {
            display: grid;
            gap: 1.5rem;
          }

          .grid-cols-1 {
            grid-template-columns: repeat(1, minmax(0, 1fr));
          }

          @media (min-width: 768px) {
            .grid-cols-2 {
              grid-template-columns: repeat(2, minmax(0, 1fr));
            }
          }

          @media (min-width: 1024px) {
            .grid-cols-3 {
              grid-template-columns: repeat(3, minmax(0, 1fr));
            }
          }

          .text-center { text-align: center; }
          .text-sm { font-size: 0.875rem; }
          .text-lg { font-size: 1.125rem; }
          .text-xl { font-size: 1.25rem; }
          .text-2xl { font-size: 1.5rem; }
          .text-3xl { font-size: 1.875rem; }
          .text-4xl { font-size: 2.25rem; }

          .font-medium { font-weight: 500; }
          .font-semibold { font-weight: 600; }
          .font-bold { font-weight: 700; }

          .text-gray-500 { color: #6b7280; }
          .text-gray-600 { color: #4b5563; }
          .text-gray-700 { color: #374151; }
          .text-gray-800 { color: #1f2937; }
          .text-blue-600 { color: #2563eb; }
          .text-green-600 { color: #16a34a; }
          .text-purple-600 { color: #9333ea; }

          .bg-blue-50 { background-color: #eff6ff; }
          .bg-green-50 { background-color: #f0fdf4; }
          .bg-purple-50 { background-color: #faf5ff; }
          .bg-gray-100 { background-color: #f3f4f6; }

          .border { border: 1px solid #e5e7eb; }
          .border-2 { border: 2px solid #e5e7eb; }
          .border-blue-200 { border-color: #bfdbfe; }
          .border-blue-300 { border-color: #93c5fd; }
          .border-blue-500 { border-color: #3b82f6; }
          .border-green-200 { border-color: #bbf7d0; }
          .border-purple-200 { border-color: #e9d5ff; }

          .rounded-lg { border-radius: 0.5rem; }

          .p-4 { padding: 1rem; }
          .p-6 { padding: 1.5rem; }
          .mb-2 { margin-bottom: 0.5rem; }
          .mb-3 { margin-bottom: 0.75rem; }
          .mb-4 { margin-bottom: 1rem; }
          .mb-6 { margin-bottom: 1.5rem; }
          .mb-8 { margin-bottom: 2rem; }
          .mt-8 { margin-top: 2rem; }
          .mt-12 { margin-top: 3rem; }

          .flex { display: flex; }
          .items-center { align-items: center; }
          .justify-between { justify-content: space-between; }

          .cursor-pointer { cursor: pointer; }
          .cursor-not-allowed { cursor: not-allowed; }
          .opacity-60 { opacity: 0.6; }

          .nav-card-active {
            border-color: #3b82f6;
            background-color: #eff6ff;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
          }

          .nav-card-disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }

          .badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
          }

          .badge-success {
            background-color: #dcfce7;
            color: #166534;
          }

          .badge-info {
            background-color: #dbeafe;
            color: #1e40af;
          }

          .badge-gray {
            background-color: #f3f4f6;
            color: #374151;
          }
        `}</style>
      </head>
      <body>
        {children}
      </body>
    </html>
  );
}
