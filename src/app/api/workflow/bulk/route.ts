/**
 * Bulk Operations API
 * Handle CSV import/export and batch processing for workflows
 */

import { NextRequest, NextResponse } from 'next/server';
import { parse } from 'csv-parse/sync';
import { stringify } from 'csv-stringify/sync';
import { workflowEngine } from '@/core/workflow/engine';
import { templateRegistry } from '@/core/workflow/templates';

interface BulkJobRequest {
  templateId: string;
  csvData?: string;
  items?: Array<Record<string, any>>;
  userApiKey?: string;
  batchSize?: number;
}

interface BulkJob {
  id: string;
  templateId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  totalItems: number;
  processedItems: number;
  failedItems: number;
  startedAt: string;
  completedAt?: string;
  results: Array<{
    index: number;
    status: 'completed' | 'failed';
    executionId?: string;
    error?: string;
    inputs: Record<string, any>;
  }>;
  progress: number;
}

// In-memory storage for bulk jobs (in production, use Redis or database)
const bulkJobs = new Map<string, BulkJob>();

/**
 * POST /api/workflow/bulk
 * Create a new bulk processing job
 */
export async function POST(request: NextRequest) {
  try {
    const data: BulkJobRequest = await request.json();
    
    if (!data.templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // Validate template exists
    const template = templateRegistry.getTemplate(data.templateId);
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    let items: Array<Record<string, any>> = [];

    // Parse CSV data if provided
    if (data.csvData) {
      try {
        const records = parse(data.csvData, {
          columns: true,
          skip_empty_lines: true,
          trim: true
        });
        items = records;
      } catch (error) {
        return NextResponse.json(
          { error: 'Invalid CSV format' },
          { status: 400 }
        );
      }
    } else if (data.items) {
      items = data.items;
    } else {
      return NextResponse.json(
        { error: 'Either csvData or items must be provided' },
        { status: 400 }
      );
    }

    if (items.length === 0) {
      return NextResponse.json(
        { error: 'No items to process' },
        { status: 400 }
      );
    }

    if (items.length > 1000) {
      return NextResponse.json(
        { error: 'Maximum 1000 items per batch' },
        { status: 400 }
      );
    }

    // Create bulk job
    const jobId = `bulk_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    const job: BulkJob = {
      id: jobId,
      templateId: data.templateId,
      status: 'pending',
      totalItems: items.length,
      processedItems: 0,
      failedItems: 0,
      startedAt: new Date().toISOString(),
      results: [],
      progress: 0
    };

    bulkJobs.set(jobId, job);

    // Start processing in background
    processBulkJob(jobId, items, data.userApiKey, data.batchSize || 5);

    return NextResponse.json({
      success: true,
      data: {
        jobId,
        status: job.status,
        totalItems: job.totalItems,
        estimatedTime: `${Math.ceil(items.length / 5)} minutes`
      }
    });

  } catch (error) {
    console.error('Bulk job creation error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create bulk job',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/workflow/bulk?jobId=xxx
 * Get bulk job status and results
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');
    const format = searchParams.get('format'); // 'csv' for export

    if (!jobId) {
      // Return all jobs
      const jobs = Array.from(bulkJobs.values()).map(job => ({
        id: job.id,
        templateId: job.templateId,
        status: job.status,
        totalItems: job.totalItems,
        processedItems: job.processedItems,
        failedItems: job.failedItems,
        progress: job.progress,
        startedAt: job.startedAt,
        completedAt: job.completedAt
      }));

      return NextResponse.json({
        success: true,
        data: { jobs }
      });
    }

    const job = bulkJobs.get(jobId);
    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }

    // Export as CSV if requested
    if (format === 'csv' && job.status === 'completed') {
      const csvData = await exportJobResultsAsCSV(job);
      
      return new NextResponse(csvData, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="bulk_results_${jobId}.csv"`
        }
      });
    }

    return NextResponse.json({
      success: true,
      data: { job }
    });

  } catch (error) {
    console.error('Bulk job retrieval error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to retrieve bulk job',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * Process bulk job in background
 */
async function processBulkJob(
  jobId: string, 
  items: Array<Record<string, any>>, 
  userApiKey?: string,
  batchSize: number = 5
) {
  const job = bulkJobs.get(jobId);
  if (!job) return;

  job.status = 'processing';
  bulkJobs.set(jobId, job);

  try {
    // Process items in batches to avoid overwhelming the system
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      
      // Process batch items in parallel
      const batchPromises = batch.map(async (item, batchIndex) => {
        const itemIndex = i + batchIndex;
        
        try {
          // Create workflow from template
          const workflowId = await workflowEngine.createWorkflow(
            templateRegistry.getTemplate(job.templateId)!.workflow
          );

          // Prepare inputs with user API key if provided
          const executionInputs = {
            ...item,
            userApiKey
          };

          // Execute workflow
          const executionId = await workflowEngine.executeWorkflow(
            workflowId,
            executionInputs,
            {
              source: 'bulk',
              priority: 'normal',
              bulkJobId: jobId
            }
          );

          job.results[itemIndex] = {
            index: itemIndex,
            status: 'completed',
            executionId,
            inputs: item
          };

          job.processedItems++;

        } catch (error) {
          console.error(`Bulk item ${itemIndex} failed:`, error);
          
          job.results[itemIndex] = {
            index: itemIndex,
            status: 'failed',
            error: error instanceof Error ? error.message : String(error),
            inputs: item
          };

          job.failedItems++;
        }

        // Update progress
        job.progress = Math.round((job.processedItems + job.failedItems) / job.totalItems * 100);
        bulkJobs.set(jobId, job);
      });

      await Promise.all(batchPromises);

      // Small delay between batches to prevent overwhelming
      if (i + batchSize < items.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    job.status = 'completed';
    job.completedAt = new Date().toISOString();
    job.progress = 100;

  } catch (error) {
    console.error(`Bulk job ${jobId} failed:`, error);
    job.status = 'failed';
  }

  bulkJobs.set(jobId, job);
}

/**
 * Export job results as CSV
 */
async function exportJobResultsAsCSV(job: BulkJob): Promise<string> {
  const records = [];
  
  for (const result of job.results) {
    const record: Record<string, any> = {
      index: result.index,
      status: result.status,
      ...result.inputs
    };

    if (result.executionId) {
      record.executionId = result.executionId;
      
      // Get execution results if available
      try {
        const execution = await workflowEngine.getExecution(result.executionId);
        if (execution && execution.outputs) {
          // Add key outputs to CSV
          Object.entries(execution.outputs).forEach(([key, value]) => {
            record[`output_${key}`] = typeof value === 'string' ? value : JSON.stringify(value);
          });
        }
      } catch (error) {
        console.error(`Failed to get execution ${result.executionId}:`, error);
      }
    }

    if (result.error) {
      record.error = result.error;
    }

    records.push(record);
  }

  return stringify(records, { header: true });
}
