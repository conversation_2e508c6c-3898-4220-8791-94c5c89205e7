/**
 * Workflow Creation API
 * Simple API to create and execute workflows
 */

import { NextRequest, NextResponse } from 'next/server';
import { WorkflowEngine } from '../../../../core/workflow/engine';
import { SimplifiedStateStore } from '../../../../core/state/store';
import { AIModelManager } from '../../../../core/ai/model-manager';
import { SimplifiedReviewSystem } from '../../../../core/review/system';
import { TemplateRegistry } from '../../../../core/workflow/templates';

// Initialize system components
const stateStore = new SimplifiedStateStore();
const aiManager = new AIModelManager({
  defaultProvider: 'openai',
  defaultModel: 'gpt-4',
  providers: {
    openai: {
      apiKey: process.env.OPENAI_API_KEY || ''
    }
  },
  costTracking: true,
  rateLimiting: {
    enabled: false,
    requestsPerMinute: 60,
    tokensPerMinute: 100000
  }
});
const reviewSystem = new SimplifiedReviewSystem(stateStore);
const workflowEngine = new WorkflowEngine(stateStore, aiManager, reviewSystem);
const templateRegistry = new TemplateRegistry();

// Initialize state store
stateStore.initialize().catch(console.error);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { templateId, inputs, userApiKey } = body;

    // Validate required fields
    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    if (!inputs) {
      return NextResponse.json(
        { error: 'Inputs are required' },
        { status: 400 }
      );
    }

    // Get template
    const template = templateRegistry.getTemplate(templateId);
    if (!template) {
      return NextResponse.json(
        { error: `Template ${templateId} not found` },
        { status: 404 }
      );
    }

    // Create workflow from template
    const workflowId = await workflowEngine.createWorkflow(template.workflow);

    // Prepare inputs with user API key if provided
    const executionInputs = {
      ...inputs,
      userApiKey // Pass user's API key for BYOK
    };

    // Execute workflow
    const executionId = await workflowEngine.executeWorkflow(
      workflowId,
      executionInputs,
      {
        source: 'api',
        priority: 'normal'
      }
    );

    return NextResponse.json({
      success: true,
      data: {
        workflowId,
        executionId,
        templateId,
        status: 'started'
      }
    });

  } catch (error) {
    console.error('Workflow creation error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to create workflow',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const executionId = searchParams.get('executionId');

    if (!executionId) {
      // Return available templates
      const templates = templateRegistry.getAllTemplates();
      
      return NextResponse.json({
        success: true,
        data: {
          templates: templates.map(template => ({
            id: template.id,
            name: template.name,
            description: template.description,
            category: template.workflow.metadata.category,
            difficulty: template.workflow.metadata.difficulty,
            estimatedTime: template.workflow.metadata.estimatedTime,
            featured: template.featured,
            sampleInputs: template.sampleInputs
          }))
        }
      });
    }

    // Get execution status
    const execution = await workflowEngine.getExecution(executionId);
    if (!execution) {
      return NextResponse.json(
        { error: 'Execution not found' },
        { status: 404 }
      );
    }

    // Get workflow details
    const workflow = await workflowEngine.getWorkflow(execution.workflowId);

    return NextResponse.json({
      success: true,
      data: {
        execution: {
          id: execution.id,
          workflowId: execution.workflowId,
          status: execution.status,
          progress: execution.progress,
          currentStep: execution.currentStep,
          startedAt: execution.startedAt,
          completedAt: execution.completedAt,
          error: execution.error
        },
        workflow: workflow ? {
          id: workflow.id,
          name: workflow.name,
          description: workflow.description
        } : null,
        steps: Object.values(execution.stepResults).map(step => ({
          stepId: step.stepId,
          status: step.status,
          startedAt: step.startedAt,
          completedAt: step.completedAt,
          duration: step.duration,
          error: step.error
        }))
      }
    });

  } catch (error) {
    console.error('Workflow status error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to get workflow status',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
