/**
 * Workflow Results API
 * Get the generated content from a completed workflow
 */

import { NextRequest, NextResponse } from 'next/server';
import { SimplifiedStateStore } from '../../../../../core/state/store';

// Initialize system components
const stateStore = new SimplifiedStateStore();

// Initialize state store
stateStore.initialize().catch(console.error);

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const executionId = params.id;

    // Get execution
    const execution = await stateStore.getExecution(executionId);
    if (!execution) {
      return NextResponse.json(
        { error: 'Execution not found' },
        { status: 404 }
      );
    }

    // Get all content items for this execution
    const state = await stateStore.get();
    if (!state) {
      return NextResponse.json(
        { error: 'No state found' },
        { status: 500 }
      );
    }

    const contentItems = Object.values(state.content).filter(
      content => content.executionId === executionId
    );

    // Get workflow details
    const workflow = await stateStore.getWorkflow(execution.workflowId);

    // Format results
    const results = {
      execution: {
        id: execution.id,
        workflowId: execution.workflowId,
        status: execution.status,
        progress: execution.progress,
        startedAt: execution.startedAt,
        completedAt: execution.completedAt,
        inputs: execution.inputs
      },
      workflow: workflow ? {
        id: workflow.id,
        name: workflow.name,
        description: workflow.description
      } : null,
      steps: Object.values(execution.stepResults).map(step => ({
        stepId: step.stepId,
        status: step.status,
        startedAt: step.startedAt,
        completedAt: step.completedAt,
        duration: step.duration,
        inputs: step.inputs,
        outputs: step.outputs,
        error: step.error
      })),
      content: contentItems.map(content => ({
        id: content.id,
        type: content.type,
        title: content.title,
        content: content.content,
        status: content.status,
        stepId: content.stepId,
        createdAt: content.createdAt,
        metadata: content.metadata
      }))
    };

    return NextResponse.json({
      success: true,
      data: results
    });

  } catch (error) {
    console.error('Results fetch error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch results',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
