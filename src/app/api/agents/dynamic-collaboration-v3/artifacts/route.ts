/**
 * Dynamic Collaboration V3 Artifacts API Route
 *
 * This file implements the API route for retrieving artifacts from the dynamic collaboration system.
 */

import { NextRequest, NextResponse } from 'next/server';
import logger from '../../../../../(payload)/api/agents/collaborative-iteration/utils/logger';
import { WorkflowOrchestrator } from '../../../../../(payload)/api/agents/dynamic-collaboration-v3';

/**
 * GET handler for retrieving artifacts from a dynamic collaboration session
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    const sessionId = req.nextUrl.searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing sessionId parameter' },
        { status: 400 }
      );
    }

    // Create orchestrator
    const orchestrator = new WorkflowOrchestrator(sessionId);

    // Get state
    const state = await orchestrator.getState();

    if (!state) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Extract artifacts
    const artifacts = state.artifacts;

    return NextResponse.json({
      success: true,
      sessionId,
      artifacts
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error retrieving artifacts for dynamic collaboration session`, {
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: err.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}


