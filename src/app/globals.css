@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Custom styles for the content generation platform */
.content-generation-dashboard {
  @apply min-h-screen bg-gray-50;
}

.error-message {
  @apply bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4;
}

.success-message {
  @apply bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4;
}

.content-type-selector {
  @apply max-w-4xl mx-auto p-6;
}

.content-type-options {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.content-type-card {
  @apply border-2 border-gray-200 rounded-lg p-6 cursor-pointer transition-all duration-200 hover:border-blue-300 hover:shadow-md;
}

.content-type-card:hover {
  @apply bg-blue-50;
}

.content-type-icon {
  @apply text-3xl mb-3;
}

/* Review system styles */
.review-assignment {
  @apply border rounded-lg p-4 mb-4 hover:shadow-md transition-shadow;
}

.review-status-pending {
  @apply bg-yellow-100 text-yellow-800;
}

.review-status-in-progress {
  @apply bg-blue-100 text-blue-800;
}

.review-status-completed {
  @apply bg-green-100 text-green-800;
}

.review-status-overdue {
  @apply bg-red-100 text-red-800;
}

/* Workflow styles */
.workflow-step {
  @apply border rounded-lg p-4 mb-3;
}

.workflow-step-completed {
  @apply border-green-300 bg-green-50;
}

.workflow-step-running {
  @apply border-blue-300 bg-blue-50;
}

.workflow-step-failed {
  @apply border-red-300 bg-red-50;
}

.workflow-step-pending {
  @apply border-gray-300 bg-gray-50;
}

/* Bulk operations styles */
.bulk-job {
  @apply border rounded-lg p-4 mb-4;
}

.bulk-job-completed {
  @apply border-green-300 bg-green-50;
}

.bulk-job-processing {
  @apply border-blue-300 bg-blue-50;
}

.bulk-job-failed {
  @apply border-red-300 bg-red-50;
}

/* Progress bars */
.progress-bar {
  @apply w-full bg-gray-200 rounded-full h-2;
}

.progress-bar-fill {
  @apply bg-blue-600 h-2 rounded-full transition-all duration-300;
}

/* Navigation styles */
.nav-card {
  @apply border-2 rounded-lg p-6 transition-all duration-200 hover:shadow-md hover:border-blue-300 cursor-pointer;
}

.nav-card-active {
  @apply border-blue-500 bg-blue-50 shadow-lg;
}

.nav-card-disabled {
  @apply opacity-60 cursor-not-allowed;
}

/* Form styles */
.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.form-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

/* Button styles */
.btn-primary {
  @apply bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-success {
  @apply bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-danger {
  @apply bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-small {
  @apply text-xs px-2 py-1 rounded;
}

/* Card styles */
.card {
  @apply bg-white border rounded-lg p-4 shadow-sm;
}

.card-header {
  @apply border-b pb-3 mb-3;
}

.card-title {
  @apply text-lg font-semibold;
}

.card-content {
  @apply text-gray-600;
}

/* Status badges */
.badge {
  @apply px-2 py-1 rounded text-xs font-medium;
}

.badge-success {
  @apply bg-green-100 text-green-700;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-700;
}

.badge-error {
  @apply bg-red-100 text-red-700;
}

.badge-info {
  @apply bg-blue-100 text-blue-700;
}

.badge-gray {
  @apply bg-gray-100 text-gray-700;
}

/* Loading states */
.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
}

.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% {
    content: '.';
  }
  40% {
    content: '..';
  }
  60%, 100% {
    content: '...';
  }
}

/* Responsive utilities */
@media (max-width: 640px) {
  .content-type-options {
    @apply grid-cols-1;
  }
  
  .nav-card {
    @apply p-4;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .card {
    @apply bg-gray-800 border-gray-700 text-white;
  }
  
  .form-input,
  .form-textarea,
  .form-select {
    @apply bg-gray-800 border-gray-600 text-white;
  }
  
  .form-label {
    @apply text-gray-300;
  }
}
