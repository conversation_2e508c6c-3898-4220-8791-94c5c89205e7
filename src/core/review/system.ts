/**
 * Simplified Review System Implementation
 * Basic approve/reject functionality with email notifications
 */

import { v4 as uuidv4 } from 'uuid';
import {
  ISimplifiedReviewSystem,
  ReviewOptions,
  ReviewLink,
  ReviewData,
  ReviewSubmission,
  ReviewDecision,
  ReviewStatus,
  ReviewContent,
  ReviewNotification,
  NotificationType,
  ReviewError,
  ReviewNotFoundError,
  ReviewExpiredError,
  ReviewAlreadyCompletedError
} from './types';
import { ISimplifiedStateStore, Review, ReviewType as StateReviewType, ReviewStatus as StateReviewStatus } from '../state/types';

export class SimplifiedReviewSystem implements ISimplifiedReviewSystem {
  constructor(
    private stateStore: ISimplifiedStateStore,
    private baseUrl: string = 'http://localhost:3000'
  ) {}

  async createReview(content: string, options: ReviewOptions): Promise<ReviewLink> {
    const reviewId = uuidv4();
    const now = new Date().toISOString();
    
    // Calculate deadline
    const deadline = options.deadline || this.calculateDefaultDeadline();
    
    // Create review in state
    const review: Review = {
      id: reviewId,
      contentId: options.contentId,
      executionId: options.executionId,
      stepId: options.stepId,
      type: this.mapReviewType(options.type),
      status: StateReviewStatus.PENDING,
      instructions: options.instructions || this.getDefaultInstructions(options.type),
      deadline,
      createdAt: now
    };

    await this.stateStore.setReview(review);

    // Create review link
    const reviewLink: ReviewLink = {
      reviewId,
      url: `${this.baseUrl}/review/${reviewId}`,
      expiresAt: deadline,
      accessToken: this.generateAccessToken(reviewId)
    };

    // Send notifications if reviewers specified
    if (options.reviewers && options.reviewers.length > 0) {
      await this.sendReviewNotifications(reviewId, options.reviewers);
    }

    return reviewLink;
  }

  async getReview(reviewId: string): Promise<ReviewData> {
    const review = await this.stateStore.getReview(reviewId);
    if (!review) {
      throw new ReviewNotFoundError(reviewId);
    }

    // Check if expired
    if (review.deadline && new Date() > new Date(review.deadline)) {
      if (review.status === StateReviewStatus.PENDING) {
        // Mark as expired
        await this.stateStore.setReview({
          ...review,
          status: StateReviewStatus.EXPIRED
        });
        throw new ReviewExpiredError(reviewId);
      }
    }

    // Get content
    const content = await this.stateStore.getContent(review.contentId);
    if (!content) {
      throw new ReviewError(`Content ${review.contentId} not found`, 'CONTENT_NOT_FOUND', reviewId);
    }

    // Get execution for context
    const execution = await this.stateStore.getExecution(review.executionId);
    const workflow = execution ? await this.stateStore.getWorkflow(execution.workflowId) : null;

    const reviewContent: ReviewContent = {
      id: content.id,
      type: content.type,
      title: content.title,
      data: content.content,
      context: {
        workflowName: workflow?.name,
        stepName: execution?.stepResults[review.stepId]?.stepId
      }
    };

    return {
      id: review.id,
      content: reviewContent,
      instructions: review.instructions,
      type: this.mapStateReviewType(review.type),
      status: this.mapStateReviewStatus(review.status),
      deadline: review.deadline,
      createdAt: review.createdAt
    };
  }

  async submitReview(reviewId: string, decision: 'approve' | 'reject', edits?: string): Promise<void> {
    const review = await this.stateStore.getReview(reviewId);
    if (!review) {
      throw new ReviewNotFoundError(reviewId);
    }

    if (review.status === StateReviewStatus.COMPLETED) {
      throw new ReviewAlreadyCompletedError(reviewId);
    }

    if (review.deadline && new Date() > new Date(review.deadline)) {
      throw new ReviewExpiredError(reviewId);
    }

    const now = new Date().toISOString();
    
    // Update review
    const updatedReview: Review = {
      ...review,
      status: StateReviewStatus.COMPLETED,
      decision: decision === 'approve' ? 'approved' : 'rejected',
      feedback: edits,
      completedAt: now
    };

    await this.stateStore.setReview(updatedReview);

    // Update content status based on decision
    const content = await this.stateStore.getContent(review.contentId);
    if (content) {
      let updatedContent = { ...content };
      
      if (decision === 'approve') {
        updatedContent.status = 'approved';
      } else {
        updatedContent.status = 'rejected';
        // If edits provided, create new version with edits
        if (edits) {
          updatedContent.content = edits;
          updatedContent.status = 'draft'; // Needs another review
        }
      }
      
      updatedContent.updatedAt = now;
      await this.stateStore.setContent(updatedContent);
    }

    // Continue workflow execution if approved
    if (decision === 'approve') {
      await this.continueWorkflowExecution(review.executionId, review.stepId);
    }

    // Send completion notification
    await this.sendCompletionNotification(reviewId, decision);
  }

  // Helper methods
  private mapReviewType(type: ReviewOptions['type']): StateReviewType {
    switch (type) {
      case 'approval':
        return StateReviewType.APPROVAL;
      case 'editing':
        return StateReviewType.EDITING;
      case 'feedback':
        return StateReviewType.FEEDBACK;
      default:
        return StateReviewType.APPROVAL;
    }
  }

  private mapStateReviewType(type: StateReviewType): ReviewOptions['type'] {
    switch (type) {
      case StateReviewType.APPROVAL:
        return 'approval';
      case StateReviewType.EDITING:
        return 'editing';
      case StateReviewType.FEEDBACK:
        return 'feedback';
      default:
        return 'approval';
    }
  }

  private mapStateReviewStatus(status: StateReviewStatus): ReviewStatus {
    switch (status) {
      case StateReviewStatus.PENDING:
        return ReviewStatus.PENDING;
      case StateReviewStatus.IN_PROGRESS:
        return ReviewStatus.IN_PROGRESS;
      case StateReviewStatus.COMPLETED:
        return ReviewStatus.COMPLETED;
      case StateReviewStatus.EXPIRED:
        return ReviewStatus.EXPIRED;
      default:
        return ReviewStatus.PENDING;
    }
  }

  private calculateDefaultDeadline(): string {
    const deadline = new Date();
    deadline.setHours(deadline.getHours() + 24); // 24 hours default
    return deadline.toISOString();
  }

  private getDefaultInstructions(type: ReviewOptions['type']): string {
    switch (type) {
      case 'approval':
        return 'Please review the content and approve or reject it. Provide feedback if rejecting.';
      case 'editing':
        return 'Please review and edit the content as needed. Make any necessary improvements.';
      case 'feedback':
        return 'Please provide feedback on the content. Suggest improvements or changes.';
      default:
        return 'Please review the content.';
    }
  }

  private generateAccessToken(reviewId: string): string {
    // Simple token generation - in production, use proper JWT or similar
    return Buffer.from(`${reviewId}:${Date.now()}`).toString('base64');
  }

  private async sendReviewNotifications(reviewId: string, reviewers: string[]): Promise<void> {
    // Simple notification - in production, integrate with email service
    console.log(`Sending review notifications for ${reviewId} to:`, reviewers);
    
    // Here you would integrate with email service like SendGrid, AWS SES, etc.
    // For now, just log the notification
    const reviewLink = `${this.baseUrl}/review/${reviewId}`;
    
    for (const reviewer of reviewers) {
      const notification: ReviewNotification = {
        type: NotificationType.REVIEW_REQUESTED,
        reviewId,
        recipient: reviewer,
        subject: 'Content Review Required',
        message: `You have been requested to review content. Please visit: ${reviewLink}`,
        link: reviewLink
      };
      
      // In production, send actual email
      console.log('Email notification:', notification);
    }
  }

  private async sendCompletionNotification(reviewId: string, decision: string): Promise<void> {
    console.log(`Review ${reviewId} completed with decision: ${decision}`);
    
    // In production, notify workflow system or other stakeholders
  }

  private async continueWorkflowExecution(executionId: string, stepId: string): Promise<void> {
    // Get execution
    const execution = await this.stateStore.getExecution(executionId);
    if (!execution) {
      return;
    }

    // Mark step as completed
    const updatedExecution = {
      ...execution,
      stepResults: {
        ...execution.stepResults,
        [stepId]: {
          ...execution.stepResults[stepId],
          status: 'completed' as any,
          completedAt: new Date().toISOString()
        }
      }
    };

    await this.stateStore.setExecution(updatedExecution);

    // In a full implementation, this would trigger the workflow engine
    // to continue with the next step
    console.log(`Continuing workflow execution ${executionId} after step ${stepId}`);
  }

  // Public utility methods
  async getPendingReviews(): Promise<Review[]> {
    return await this.stateStore.getPendingReviews();
  }

  async getReviewsByStatus(status: StateReviewStatus): Promise<Review[]> {
    const state = await this.stateStore.get();
    if (!state) return [];
    
    return Object.values(state.reviews).filter(review => review.status === status);
  }

  async expireOverdueReviews(): Promise<void> {
    const pendingReviews = await this.getPendingReviews();
    const now = new Date();
    
    for (const review of pendingReviews) {
      if (review.deadline && now > new Date(review.deadline)) {
        await this.stateStore.setReview({
          ...review,
          status: StateReviewStatus.EXPIRED
        });
      }
    }
  }
}
