/**
 * AI Model Manager
 * Central manager for all AI providers with BYOK support and model selection
 */

import { 
  AIProvider, 
  GenerationOptions, 
  GenerationResult, 
  AIModelManagerConfig,
  ModelSelectionCriteria,
  ModelRecommendation,
  UsageStats,
  AIModelConfig,
  ModelCapability,
  AIProviderError
} from './types';
import { OpenAIProvider } from './providers/openai-provider';
import { AnthropicProvider } from './providers/anthropic-provider';

export class AIModelManager {
  private providers = new Map<string, AIProvider>();
  private usageStats = new Map<string, UsageStats>();
  private rateLimitTracker = new Map<string, { requests: number; tokens: number; resetTime: number }>();

  constructor(private config: AIModelManagerConfig) {
    this.initializeProviders();
  }

  private initializeProviders(): void {
    // Initialize OpenAI provider
    if (this.config.providers.openai) {
      this.providers.set('openai', new OpenAIProvider(this.config.providers.openai));
    }

    // Initialize Anthropic provider
    if (this.config.providers.anthropic) {
      this.providers.set('anthropic', new AnthropicProvider(this.config.providers.anthropic));
    }

    // Add more providers as needed
  }

  /**
   * Generate content using specified model or auto-select best model
   */
  async generate(
    prompt: string, 
    options: Partial<GenerationOptions> & { 
      provider?: string;
      userApiKey?: string;
      criteria?: ModelSelectionCriteria;
    } = {}
  ): Promise<GenerationResult> {
    let provider: string;
    let model: string;

    if (options.provider && options.model) {
      // User specified exact provider and model
      provider = options.provider;
      model = options.model;
    } else if (options.criteria) {
      // Auto-select based on criteria
      const recommendation = await this.recommendModel(options.criteria);
      provider = recommendation.provider;
      model = recommendation.model;
    } else {
      // Use defaults
      provider = this.config.defaultProvider;
      model = this.config.defaultModel;
    }

    const aiProvider = this.providers.get(provider);
    if (!aiProvider) {
      throw new AIProviderError(
        `Provider ${provider} not available`,
        provider,
        model,
        'PROVIDER_NOT_FOUND'
      );
    }

    // Check rate limits
    if (this.config.rateLimiting.enabled) {
      await this.checkRateLimit(provider, model);
    }

    // Prepare generation options
    const generationOptions: GenerationOptions = {
      model,
      apiKey: options.userApiKey,
      temperature: options.temperature ?? 0.7,
      maxTokens: options.maxTokens ?? 2000,
      systemPrompt: options.systemPrompt,
      stopSequences: options.stopSequences,
      stream: options.stream ?? false
    };

    const startTime = Date.now();
    
    try {
      const result = await aiProvider.generate(prompt, generationOptions);
      
      // Track usage
      if (this.config.costTracking) {
        this.trackUsage(provider, model, result, Date.now() - startTime);
      }

      // Update rate limit tracker
      if (this.config.rateLimiting.enabled) {
        this.updateRateLimit(provider, model, result.usage.totalTokens);
      }

      return result;

    } catch (error) {
      // Track error
      this.trackError(provider, model);
      throw error;
    }
  }

  /**
   * Recommend the best model based on criteria
   */
  async recommendModel(criteria: ModelSelectionCriteria): Promise<ModelRecommendation> {
    const models = this.getAvailableModels();
    let bestModel = models[0];
    let bestScore = 0;
    let reasoning = '';

    for (const modelConfig of models) {
      let score = 0;
      const reasons: string[] = [];

      // Task-based scoring
      if (criteria.task === 'content_generation' && 
          modelConfig.capabilities.includes(ModelCapability.CREATIVE_WRITING)) {
        score += 30;
        reasons.push('excellent for content generation');
      }

      if (criteria.task === 'seo_optimization' && 
          modelConfig.capabilities.includes(ModelCapability.SEO_OPTIMIZATION)) {
        score += 30;
        reasons.push('optimized for SEO tasks');
      }

      // Quality vs speed scoring
      if (criteria.quality === 'high' && modelConfig.model.includes('gpt-4')) {
        score += 25;
        reasons.push('high quality model');
      } else if (criteria.quality === 'fast' && modelConfig.model.includes('3.5')) {
        score += 25;
        reasons.push('fast response time');
      }

      // Budget scoring
      if (criteria.budget === 'low' && modelConfig.costPerToken < 0.002) {
        score += 20;
        reasons.push('cost-effective');
      } else if (criteria.budget === 'high' && modelConfig.costPerToken > 0.01) {
        score += 15;
        reasons.push('premium model');
      }

      // Content length scoring
      if (criteria.contentLength === 'long' && modelConfig.maxTokens > 8000) {
        score += 15;
        reasons.push('supports long content');
      }

      if (score > bestScore) {
        bestScore = score;
        bestModel = modelConfig;
        reasoning = reasons.join(', ');
      }
    }

    const estimatedTokens = this.estimateTokensForTask(criteria);
    
    return {
      provider: bestModel.provider,
      model: bestModel.model,
      confidence: Math.min(bestScore / 100, 1),
      reasoning,
      estimatedCost: estimatedTokens * bestModel.costPerToken,
      estimatedTime: this.estimateResponseTime(bestModel.provider, bestModel.model)
    };
  }

  /**
   * Get available models with their configurations
   */
  getAvailableModels(): AIModelConfig[] {
    const models: AIModelConfig[] = [];

    // OpenAI models
    if (this.providers.has('openai')) {
      models.push(
        {
          provider: 'openai',
          model: 'gpt-4',
          defaultOptions: { temperature: 0.7, maxTokens: 2000 },
          costPerToken: 0.045, // Average of input/output
          maxTokens: 8192,
          description: 'Most capable GPT-4 model',
          capabilities: [
            ModelCapability.TEXT_GENERATION,
            ModelCapability.CREATIVE_WRITING,
            ModelCapability.TECHNICAL_WRITING,
            ModelCapability.ANALYSIS
          ]
        },
        {
          provider: 'openai',
          model: 'gpt-3.5-turbo',
          defaultOptions: { temperature: 0.7, maxTokens: 2000 },
          costPerToken: 0.00175,
          maxTokens: 4096,
          description: 'Fast and cost-effective model',
          capabilities: [
            ModelCapability.TEXT_GENERATION,
            ModelCapability.CREATIVE_WRITING
          ]
        }
      );
    }

    // Anthropic models
    if (this.providers.has('anthropic')) {
      models.push(
        {
          provider: 'anthropic',
          model: 'claude-3-opus-20240229',
          defaultOptions: { temperature: 0.7, maxTokens: 2000 },
          costPerToken: 0.045,
          maxTokens: 200000,
          description: 'Most capable Claude model',
          capabilities: [
            ModelCapability.TEXT_GENERATION,
            ModelCapability.CREATIVE_WRITING,
            ModelCapability.TECHNICAL_WRITING,
            ModelCapability.ANALYSIS
          ]
        },
        {
          provider: 'anthropic',
          model: 'claude-3-haiku-20240307',
          defaultOptions: { temperature: 0.7, maxTokens: 2000 },
          costPerToken: 0.000625,
          maxTokens: 200000,
          description: 'Fast and cost-effective Claude model',
          capabilities: [
            ModelCapability.TEXT_GENERATION,
            ModelCapability.CREATIVE_WRITING
          ]
        }
      );
    }

    return models;
  }

  /**
   * Validate API key for a provider
   */
  async validateApiKey(provider: string, apiKey: string): Promise<boolean> {
    const aiProvider = this.providers.get(provider);
    if (!aiProvider) {
      return false;
    }

    return await aiProvider.validateApiKey(apiKey);
  }

  /**
   * Get usage statistics
   */
  getUsageStats(): UsageStats[] {
    return Array.from(this.usageStats.values());
  }

  private async checkRateLimit(provider: string, model: string): Promise<void> {
    const key = `${provider}:${model}`;
    const now = Date.now();
    const tracker = this.rateLimitTracker.get(key);

    if (!tracker || now > tracker.resetTime) {
      // Reset or initialize tracker
      this.rateLimitTracker.set(key, {
        requests: 0,
        tokens: 0,
        resetTime: now + 60000 // 1 minute window
      });
      return;
    }

    if (tracker.requests >= this.config.rateLimiting.requestsPerMinute) {
      throw new AIProviderError(
        'Rate limit exceeded: too many requests',
        provider,
        model,
        'RATE_LIMIT_REQUESTS'
      );
    }

    if (tracker.tokens >= this.config.rateLimiting.tokensPerMinute) {
      throw new AIProviderError(
        'Rate limit exceeded: too many tokens',
        provider,
        model,
        'RATE_LIMIT_TOKENS'
      );
    }
  }

  private updateRateLimit(provider: string, model: string, tokens: number): void {
    const key = `${provider}:${model}`;
    const tracker = this.rateLimitTracker.get(key);
    
    if (tracker) {
      tracker.requests += 1;
      tracker.tokens += tokens;
    }
  }

  private trackUsage(provider: string, model: string, result: GenerationResult, responseTime: number): void {
    const key = `${provider}:${model}`;
    const existing = this.usageStats.get(key);

    if (existing) {
      existing.totalRequests += 1;
      existing.totalTokens += result.usage.totalTokens;
      existing.totalCost += result.cost || 0;
      existing.averageResponseTime = (existing.averageResponseTime + responseTime) / 2;
      existing.lastUsed = new Date().toISOString();
    } else {
      this.usageStats.set(key, {
        provider,
        model,
        totalRequests: 1,
        totalTokens: result.usage.totalTokens,
        totalCost: result.cost || 0,
        averageResponseTime: responseTime,
        errorRate: 0,
        lastUsed: new Date().toISOString()
      });
    }
  }

  private trackError(provider: string, model: string): void {
    const key = `${provider}:${model}`;
    const existing = this.usageStats.get(key);
    
    if (existing) {
      const totalAttempts = existing.totalRequests + 1;
      const errors = existing.errorRate * existing.totalRequests + 1;
      existing.errorRate = errors / totalAttempts;
    }
  }

  private estimateTokensForTask(criteria: ModelSelectionCriteria): number {
    const baseTokens = {
      'content_generation': 1500,
      'keyword_research': 500,
      'seo_optimization': 800,
      'analysis': 1000
    };

    const lengthMultiplier = {
      'short': 0.5,
      'medium': 1,
      'long': 2
    };

    return baseTokens[criteria.task] * lengthMultiplier[criteria.contentLength];
  }

  private estimateResponseTime(provider: string, model: string): number {
    // Rough estimates in milliseconds
    const estimates: Record<string, number> = {
      'openai:gpt-4': 8000,
      'openai:gpt-3.5-turbo': 3000,
      'anthropic:claude-3-opus-20240229': 10000,
      'anthropic:claude-3-haiku-20240307': 4000
    };

    return estimates[`${provider}:${model}`] || 5000;
  }
}
