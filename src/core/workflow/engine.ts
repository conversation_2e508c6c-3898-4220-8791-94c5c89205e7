/**
 * Simplified Workflow Engine
 * Basic workflow execution without complex orchestration
 * Fixed execution scope issue
 */

import { v4 as uuidv4 } from 'uuid';
import {
  IWorkflowEngine,
  Workflow,
  WorkflowExecution,
  WorkflowStep,
  StepResult,
  StepType,
  ExecutionStatus,
  StepStatus,
  WorkflowFilters,
  ReviewDecision,
  VariableContext,
  ExecutionMetadata
} from './types';
import { ISimplifiedStateStore, ContentItem, ContentType, ContentStatus } from '../state/types';
import { AIModelManager } from '../ai/model-manager';
import { SimplifiedReviewSystem } from '../review/system';

export class WorkflowEngine implements IWorkflowEngine {
  constructor(
    private stateStore: ISimplifiedStateStore,
    private aiManager: AIModelManager,
    private reviewSystem: SimplifiedReviewSystem
  ) {}

  // Workflow management
  async createWorkflow(workflow: Omit<Workflow, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const id = uuidv4();
    const now = new Date().toISOString();

    const newWorkflow: Workflow = {
      ...workflow,
      id,
      createdAt: now,
      updatedAt: now
    };

    await this.stateStore.setWorkflow(newWorkflow);
    return id;
  }

  async getWorkflow(id: string): Promise<Workflow | null> {
    return await this.stateStore.getWorkflow(id);
  }

  async updateWorkflow(id: string, updates: Partial<Workflow>): Promise<void> {
    const workflow = await this.getWorkflow(id);
    if (!workflow) {
      throw new Error(`Workflow ${id} not found`);
    }

    const updatedWorkflow: Workflow = {
      ...workflow,
      ...updates,
      updatedAt: new Date().toISOString()
    };

    await this.stateStore.setWorkflow(updatedWorkflow);
  }

  async deleteWorkflow(id: string): Promise<void> {
    // In a full implementation, this would remove the workflow
    // For now, just mark as deleted or implement soft delete
    throw new Error('Delete workflow not implemented yet');
  }

  async listWorkflows(filters?: WorkflowFilters): Promise<Workflow[]> {
    const workflows = await this.stateStore.getAllWorkflows();

    if (!filters) {
      return workflows;
    }

    return workflows.filter(workflow => {
      if (filters.category && workflow.metadata.category !== filters.category) {
        return false;
      }
      if (filters.difficulty && workflow.metadata.difficulty !== filters.difficulty) {
        return false;
      }
      if (filters.featured !== undefined && workflow.metadata.featured !== filters.featured) {
        return false;
      }
      if (filters.tags && !filters.tags.some(tag => workflow.metadata.tags.includes(tag))) {
        return false;
      }
      return true;
    });
  }

  // Execution management
  async executeWorkflow(
    workflowId: string,
    inputs: Record<string, any>,
    metadata?: Partial<ExecutionMetadata>
  ): Promise<string> {
    const workflow = await this.getWorkflow(workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${workflowId} not found`);
    }

    const executionId = uuidv4();
    const now = new Date().toISOString();

    const execution: WorkflowExecution = {
      id: executionId,
      workflowId,
      status: ExecutionStatus.RUNNING,
      inputs,
      outputs: {},
      stepResults: {},
      progress: 0,
      startedAt: now,
      metadata: {
        source: 'api',
        priority: 'normal',
        ...metadata
      }
    };

    await this.stateStore.setExecution(execution);

    // Start execution asynchronously
    this.executeWorkflowSteps(executionId).catch(error => {
      console.error(`Workflow execution ${executionId} failed:`, error);
      this.markExecutionFailed(executionId, error);
    });

    return executionId;
  }

  async getExecution(id: string): Promise<WorkflowExecution | null> {
    return await this.stateStore.getExecution(id);
  }

  async pauseExecution(id: string): Promise<void> {
    const execution = await this.getExecution(id);
    if (!execution) {
      throw new Error(`Execution ${id} not found`);
    }

    const updatedExecution: WorkflowExecution = {
      ...execution,
      status: ExecutionStatus.PAUSED
    };

    await this.stateStore.setExecution(updatedExecution);
  }

  async resumeExecution(id: string): Promise<void> {
    const execution = await this.getExecution(id);
    if (!execution) {
      throw new Error(`Execution ${id} not found`);
    }

    const updatedExecution: WorkflowExecution = {
      ...execution,
      status: ExecutionStatus.RUNNING
    };

    await this.stateStore.setExecution(updatedExecution);

    // Resume execution
    this.executeWorkflowSteps(id).catch(error => {
      console.error(`Workflow execution ${id} failed on resume:`, error);
      this.markExecutionFailed(id, error);
    });
  }

  async cancelExecution(id: string): Promise<void> {
    const execution = await this.getExecution(id);
    if (!execution) {
      throw new Error(`Execution ${id} not found`);
    }

    const updatedExecution: WorkflowExecution = {
      ...execution,
      status: ExecutionStatus.CANCELLED,
      completedAt: new Date().toISOString()
    };

    await this.stateStore.setExecution(updatedExecution);
  }

  // Step execution
  async executeStep(executionId: string, stepId: string): Promise<StepResult> {
    const execution = await this.getExecution(executionId);
    if (!execution) {
      throw new Error(`Execution ${executionId} not found`);
    }

    const workflow = await this.getWorkflow(execution.workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${execution.workflowId} not found`);
    }

    const step = workflow.steps.find(s => s.id === stepId);
    if (!step) {
      throw new Error(`Step ${stepId} not found in workflow`);
    }

    return await this.executeWorkflowStep(execution, step);
  }

  async retryStep(executionId: string, stepId: string): Promise<StepResult> {
    // Reset step status and re-execute
    const execution = await this.getExecution(executionId);
    if (!execution) {
      throw new Error(`Execution ${executionId} not found`);
    }

    // Remove previous step result
    const updatedExecution = {
      ...execution,
      stepResults: {
        ...execution.stepResults,
        [stepId]: undefined
      }
    };
    delete updatedExecution.stepResults[stepId];

    await this.stateStore.setExecution(updatedExecution);

    return await this.executeStep(executionId, stepId);
  }

  // Review handling
  async submitReview(executionId: string, stepId: string, decision: ReviewDecision): Promise<void> {
    // This would be called by the review system
    // For now, just update the step status
    const execution = await this.getExecution(executionId);
    if (!execution) {
      throw new Error(`Execution ${executionId} not found`);
    }

    const stepResult = execution.stepResults[stepId];
    if (!stepResult) {
      throw new Error(`Step result ${stepId} not found`);
    }

    const updatedStepResult: StepResult = {
      ...stepResult,
      status: decision.approved ? StepStatus.COMPLETED : StepStatus.FAILED,
      completedAt: new Date().toISOString(),
      outputs: {
        ...stepResult.outputs,
        review_decision: decision.approved ? 'approved' : 'rejected',
        review_feedback: decision.feedback
      }
    };

    const updatedExecution: WorkflowExecution = {
      ...execution,
      stepResults: {
        ...execution.stepResults,
        [stepId]: updatedStepResult
      }
    };

    await this.stateStore.setExecution(updatedExecution);

    // Continue execution if approved
    if (decision.approved) {
      this.executeWorkflowSteps(executionId).catch(error => {
        console.error(`Workflow execution ${executionId} failed after review:`, error);
        this.markExecutionFailed(executionId, error);
      });
    }
  }

  // Private methods for workflow execution
  private async executeWorkflowSteps(executionId: string): Promise<void> {
    const execution = await this.getExecution(executionId);
    if (!execution || execution.status !== ExecutionStatus.RUNNING) {
      return;
    }

    const workflow = await this.getWorkflow(execution.workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${execution.workflowId} not found`);
    }

    // Find next step to execute
    const nextStep = this.findNextStep(workflow, execution);
    if (!nextStep) {
      // All steps completed
      await this.markExecutionCompleted(executionId);
      return;
    }

    // Check if step is waiting for review
    const stepResult = execution.stepResults[nextStep.id];
    if (stepResult && stepResult.status === StepStatus.WAITING_REVIEW) {
      return; // Wait for review
    }

    // Execute the step
    try {
      await this.executeWorkflowStep(execution, nextStep);

      // Continue with next step
      setTimeout(() => {
        this.executeWorkflowSteps(executionId).catch(error => {
          console.error(`Workflow execution ${executionId} failed:`, error);
          this.markExecutionFailed(executionId, error);
        });
      }, 100); // Small delay to prevent stack overflow

    } catch (error) {
      console.error(`Step ${nextStep.id} failed:`, error);
      await this.markStepFailed(executionId, nextStep.id, error);
      throw error;
    }
  }

  private findNextStep(workflow: Workflow, execution: WorkflowExecution): WorkflowStep | null {
    for (const step of workflow.steps) {
      const stepResult = execution.stepResults[step.id];

      // Skip completed steps
      if (stepResult && stepResult.status === StepStatus.COMPLETED) {
        continue;
      }

      // Skip failed steps (would need retry)
      if (stepResult && stepResult.status === StepStatus.FAILED) {
        continue;
      }

      // Skip steps waiting for review
      if (stepResult && stepResult.status === StepStatus.WAITING_REVIEW) {
        continue;
      }

      // Check dependencies
      const dependenciesMet = step.dependencies.every(depId => {
        const depResult = execution.stepResults[depId];
        return depResult && depResult.status === StepStatus.COMPLETED;
      });

      if (dependenciesMet) {
        return step;
      }
    }

    return null;
  }

  private async executeWorkflowStep(execution: WorkflowExecution, step: WorkflowStep): Promise<StepResult> {
    const startTime = new Date().toISOString();

    // Create initial step result
    const stepResult: StepResult = {
      stepId: step.id,
      status: StepStatus.RUNNING,
      inputs: this.buildStepInputs(execution, step),
      outputs: {},
      startedAt: startTime
    };

    // Update execution with running step
    await this.updateStepResult(execution.id, step.id, stepResult);

    try {
      let outputs: Record<string, any> = {};

      switch (step.type) {
        case StepType.TEXT_INPUT:
          outputs = await this.executeTextInput(step, stepResult.inputs);
          break;
        case StepType.AI_GENERATION:
          outputs = await this.executeAIGeneration(step, stepResult.inputs, execution.id);
          break;
        case StepType.HUMAN_REVIEW:
          outputs = await this.executeHumanReview(execution, step, stepResult.inputs);
          break;
        default:
          throw new Error(`Step type ${step.type} not implemented`);
      }

      // Mark step as completed
      const completedResult: StepResult = {
        ...stepResult,
        status: StepStatus.COMPLETED,
        outputs,
        completedAt: new Date().toISOString(),
        duration: Date.now() - new Date(startTime).getTime()
      };

      await this.updateStepResult(execution.id, step.id, completedResult);
      return completedResult;

    } catch (error) {
      const failedResult: StepResult = {
        ...stepResult,
        status: StepStatus.FAILED,
        error: error instanceof Error ? error.message : String(error),
        completedAt: new Date().toISOString(),
        duration: Date.now() - new Date(startTime).getTime()
      };

      await this.updateStepResult(execution.id, step.id, failedResult);
      throw error;
    }
  }

  private buildStepInputs(execution: WorkflowExecution, step: WorkflowStep): Record<string, any> {
    const context: VariableContext = {
      ...execution.inputs
    };

    // Add outputs from previous steps
    for (const [stepId, result] of Object.entries(execution.stepResults)) {
      if (result.status === StepStatus.COMPLETED) {
        Object.assign(context, result.outputs);
      }
    }

    // Extract only the inputs this step needs
    const stepInputs: Record<string, any> = {};
    for (const inputName of step.inputs) {
      if (context[inputName] !== undefined) {
        stepInputs[inputName] = context[inputName];
      }
    }

    return stepInputs;
  }

  private async executeTextInput(step: WorkflowStep, inputs: Record<string, any>): Promise<Record<string, any>> {
    // For text input steps, the inputs are already provided in the execution inputs
    // Just pass them through as outputs
    const outputs: Record<string, any> = {};

    for (const outputName of step.outputs) {
      if (inputs[outputName] !== undefined) {
        outputs[outputName] = inputs[outputName];
      }
    }

    return outputs;
  }

  private async executeAIGeneration(step: WorkflowStep, inputs: Record<string, any>, executionId?: string): Promise<Record<string, any>> {
    const aiConfig = step.config.aiConfig;
    if (!aiConfig) {
      throw new Error('AI configuration not found for AI generation step');
    }

    // Replace variables in prompt
    let prompt = aiConfig.prompt;
    for (const [key, value] of Object.entries(inputs)) {
      prompt = prompt.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
    }

    // Generate content using AI
    const result = await this.aiManager.generate(prompt, {
      provider: aiConfig.provider,
      model: aiConfig.model,
      temperature: aiConfig.temperature,
      maxTokens: aiConfig.maxTokens,
      systemPrompt: aiConfig.systemPrompt,
      userApiKey: aiConfig.userApiKey
    });

    // Create content item
    const contentId = uuidv4();
    const contentItem: ContentItem = {
      id: contentId,
      type: this.inferContentType(step.type),
      title: step.name,
      content: result.content,
      status: ContentStatus.DRAFT,
      executionId: executionId || '',
      stepId: step.id,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      metadata: {
        aiModel: result.model,
        aiProvider: result.provider,
        cost: result.cost,
        wordCount: result.content.split(' ').length
      }
    };

    await this.stateStore.setContent(contentItem);

    // Return outputs
    const outputs: Record<string, any> = {
      content_id: contentId,
      content: result.content
    };

    // Map to step outputs
    for (const outputName of step.outputs) {
      if (outputName === 'content' || outputName.includes('content')) {
        outputs[outputName] = result.content;
      } else if (outputName.includes('id')) {
        outputs[outputName] = contentId;
      }
    }

    return outputs;
  }

  private async executeHumanReview(
    execution: WorkflowExecution,
    step: WorkflowStep,
    inputs: Record<string, any>
  ): Promise<Record<string, any>> {
    const reviewConfig = step.config.reviewConfig;
    if (!reviewConfig) {
      throw new Error('Review configuration not found for human review step');
    }

    // Find content to review
    let contentToReview = '';
    let contentId = '';

    for (const [key, value] of Object.entries(inputs)) {
      if (key.includes('content') && typeof value === 'string') {
        contentToReview = value;
      }
      if (key.includes('id') || key.includes('content_id')) {
        contentId = String(value);
      }
    }

    if (!contentToReview) {
      throw new Error('No content found to review');
    }

    // Create review
    const reviewLink = await this.reviewSystem.createReview(contentToReview, {
      contentId: contentId || uuidv4(),
      executionId: execution.id,
      stepId: step.id,
      type: reviewConfig.reviewType as any,
      instructions: reviewConfig.instructions,
      deadline: reviewConfig.deadline,
      reviewers: reviewConfig.reviewers
    });

    // Return review info (step will be completed when review is submitted)
    return {
      review_id: reviewLink.reviewId,
      review_url: reviewLink.url,
      status: 'waiting_review'
    };
  }

  private inferContentType(stepType: StepType): ContentType {
    switch (stepType) {
      case StepType.AI_GENERATION:
        return ContentType.GENERIC;
      case StepType.KEYWORD_RESEARCH:
        return ContentType.KEYWORD_RESEARCH;
      case StepType.CONTENT_CREATION:
        return ContentType.BLOG_POST;
      case StepType.SEO_OPTIMIZATION:
        return ContentType.SEO_ANALYSIS;
      default:
        return ContentType.GENERIC;
    }
  }

  private async updateStepResult(executionId: string, stepId: string, stepResult: StepResult): Promise<void> {
    const execution = await this.getExecution(executionId);
    if (!execution) {
      throw new Error(`Execution ${executionId} not found`);
    }

    const updatedExecution: WorkflowExecution = {
      ...execution,
      stepResults: {
        ...execution.stepResults,
        [stepId]: stepResult
      },
      currentStep: stepResult.status === StepStatus.RUNNING ? stepId : execution.currentStep,
      progress: this.calculateProgress(execution.stepResults, stepId, stepResult)
    };

    await this.stateStore.setExecution(updatedExecution);
  }

  private calculateProgress(stepResults: Record<string, StepResult>, currentStepId: string, currentResult: StepResult): number {
    const allResults = { ...stepResults, [currentStepId]: currentResult };
    const totalSteps = Object.keys(allResults).length;
    const completedSteps = Object.values(allResults).filter(r => r.status === StepStatus.COMPLETED).length;

    return Math.round((completedSteps / totalSteps) * 100);
  }

  private async markExecutionCompleted(executionId: string): Promise<void> {
    const execution = await this.getExecution(executionId);
    if (!execution) return;

    const updatedExecution: WorkflowExecution = {
      ...execution,
      status: ExecutionStatus.COMPLETED,
      progress: 100,
      completedAt: new Date().toISOString()
    };

    await this.stateStore.setExecution(updatedExecution);
  }

  private async markExecutionFailed(executionId: string, error: any): Promise<void> {
    const execution = await this.getExecution(executionId);
    if (!execution) return;

    const updatedExecution: WorkflowExecution = {
      ...execution,
      status: ExecutionStatus.FAILED,
      error: {
        message: error instanceof Error ? error.message : String(error),
        code: 'EXECUTION_FAILED',
        recoverable: true,
        timestamp: new Date().toISOString()
      },
      completedAt: new Date().toISOString()
    };

    await this.stateStore.setExecution(updatedExecution);
  }

  private async markStepFailed(executionId: string, stepId: string, error: any): Promise<void> {
    const stepResult: StepResult = {
      stepId,
      status: StepStatus.FAILED,
      inputs: {},
      outputs: {},
      startedAt: new Date().toISOString(),
      completedAt: new Date().toISOString(),
      error: error instanceof Error ? error.message : String(error)
    };

    await this.updateStepResult(executionId, stepId, stepResult);
  }
}
