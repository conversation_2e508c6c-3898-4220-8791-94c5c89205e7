/**
 * Singleton instances for workflow system
 * Ensures all API routes use the same instances
 */

import { WorkflowEngine } from './engine';
import { SimplifiedStateStore } from '../state/store';
import { AIModelManager } from '../ai/model-manager';
import { SimplifiedReviewSystem } from '../review/system';
import { TemplateRegistry } from './templates';

// Create singleton instances
let stateStoreInstance: SimplifiedStateStore | null = null;
let aiManagerInstance: AIModelManager | null = null;
let reviewSystemInstance: SimplifiedReviewSystem | null = null;
let workflowEngineInstance: WorkflowEngine | null = null;
let templateRegistryInstance: TemplateRegistry | null = null;

export function getStateStore(): SimplifiedStateStore {
  if (!stateStoreInstance) {
    stateStoreInstance = new SimplifiedStateStore();
    stateStoreInstance.initialize().catch(console.error);
  }
  return stateStoreInstance;
}

export function getAIManager(): AIModelManager {
  if (!aiManagerInstance) {
    aiManagerInstance = new AIModelManager({
      defaultProvider: 'openai',
      defaultModel: 'gpt-4',
      providers: {
        openai: {
          apiKey: process.env.OPENAI_API_KEY || ''
        }
      },
      costTracking: true,
      rateLimiting: {
        enabled: false,
        requestsPerMinute: 60,
        tokensPerMinute: 100000
      }
    });
  }
  return aiManagerInstance;
}

export function getReviewSystem(): SimplifiedReviewSystem {
  if (!reviewSystemInstance) {
    reviewSystemInstance = new SimplifiedReviewSystem(getStateStore());
  }
  return reviewSystemInstance;
}

export function getWorkflowEngine(): WorkflowEngine {
  if (!workflowEngineInstance) {
    workflowEngineInstance = new WorkflowEngine(
      getStateStore(),
      getAIManager(),
      getReviewSystem()
    );
  }
  return workflowEngineInstance;
}

export function getTemplateRegistry(): TemplateRegistry {
  if (!templateRegistryInstance) {
    templateRegistryInstance = new TemplateRegistry();
  }
  return templateRegistryInstance;
}
